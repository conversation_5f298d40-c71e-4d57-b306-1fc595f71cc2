package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import com.ly.titc.pms.member.dubbo.entity.request.BasePageReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author：rui
 * @name：PageUpgradeRuleReq
 * @Date：2024-11-7 20:21
 * @Filename：PageUpgradeRuleReq
 */
@Data
@Accessors(chain = true)
public class PageCardLevelUpgradeRuleReq extends BasePageReq {

    private String name;

    private Integer sourceLevel;

    private Integer state;


}
