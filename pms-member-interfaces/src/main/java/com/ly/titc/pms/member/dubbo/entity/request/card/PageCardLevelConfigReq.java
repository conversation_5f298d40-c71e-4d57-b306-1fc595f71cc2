package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BasePageReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 分页查询会员等级入参
 *
 * @Author：rui
 * @name：MemberCardPrivilegeConfigInfoQueryReq
 * @Date：2024-11-7 17:52
 * @Filename：MemberCardPrivilegeConfigInfoQueryReq
 */
@Data
public class PageCardLevelConfigReq extends BasePageReq {

    /**
     * 卡id
     */
    private Long cardId;

    /**
     * 卡等级
     */
    private Integer level;

    /**
     * 状态
     */
    private Integer state;

}
