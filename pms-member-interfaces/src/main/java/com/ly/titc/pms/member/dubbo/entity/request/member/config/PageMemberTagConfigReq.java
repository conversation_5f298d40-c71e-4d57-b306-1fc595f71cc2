package com.ly.titc.pms.member.dubbo.entity.request.member.config;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseMasterReq;
import com.ly.titc.pms.member.dubbo.entity.request.BasePageReq;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author：rui
 * @name：PageMemberTagConfigInfoReq
 * @Date：2024-11-8 11:37
 * @Filename：PageMemberTagConfigInfoReq
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageMemberTagConfigReq extends BasePageReq {

    private Integer type;

    private List<Integer> typeList;

    private String name;

}
