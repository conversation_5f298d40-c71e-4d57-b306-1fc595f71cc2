package com.ly.titc.pms.member.dubbo.entity.request;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 分页查询请求体
 *
 * @classname BasePageReq
 * @descrition 分页查询请求体
 * <AUTHOR>
 * @since 2023/6/28 15:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class BasePageReq extends BaseMasterReq {

    /**
     * 页码
     */
    @Min(value = 1, message = "页码非法，最小为1")
    private Integer pageIndex = 1;

    /**
     * pageSize
     */
    @Max(value = 100, message = "页数非法，最大为100")
    private Integer pageSize = 20;
}
