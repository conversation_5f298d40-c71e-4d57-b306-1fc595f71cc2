package com.ly.titc.pms.member.dubbo.entity.request.order;

import com.ly.titc.pms.member.dubbo.entity.request.BasePageReq;
import lombok.Data;

/**
 * @Author：rui
 * @name：PageMemberStoreConsumeRequest
 * @Date：2024-12-9 16:13
 * @Filename：PageMemberStoreConsumeRequest
 */
@Data
public class PageMemberStoreRecordReq extends BasePageReq {

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 消费类型：PAY:消费，FREEZE, REFUND 退款
     */
    private String consumeType;

    /**
     * 渠道
     */
    private String platformChannel;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 订单状态 1 未支付 2 支付成功  3 已退款
     */
    private Integer state;

}
