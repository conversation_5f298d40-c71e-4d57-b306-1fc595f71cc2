package com.ly.titc.pms.member.mediator.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.Message;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.member.biz.*;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.pms.member.com.constant.TurboMqTopicTag;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.com.utils.PageUtil;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.dubbo.entity.request.data.GetMemberPayRegisterStatisticsReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberPayRegisterStatisticsResp;
import com.ly.titc.pms.member.entity.bo.PageMemberCheckInParamBo;
import com.ly.titc.pms.member.mediator.converter.MemberDataRecordMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.card.*;
import com.ly.titc.pms.member.mediator.entity.dto.data.*;
import com.ly.titc.pms.member.mediator.entity.message.MemberCheckInSummaryMsgExt;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.service.CardConfigMedService;
import com.ly.titc.pms.member.mediator.service.MemberDataRecordMedService;
import com.ly.titc.pms.spm.dubbo.enums.MasterTypeEnum;
import com.ly.titc.springboot.mq.starter.core.producer.MessageHelper;
import com.ly.titc.springboot.mq.starter.core.producer.TurboMqProducerTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberDataRecordMedServiceImp
 * @Date：2024-12-11 21:43
 * @Filename：MemberDataRecordMedServiceImp
 */
@Slf4j
@Service
public class MemberDataRecordMedServiceImp implements MemberDataRecordMedService {

    @Resource
    private MemberDataRecordMedConverter memberDataRecordMedConverter;

    @Resource
    private MemberCheckInRecordBiz checkInRecordBiz;

    @Resource
    private MemberCheckInStatisticsBiz checkInStatisticsBiz;

    @Resource
    private MemberOrderInfoBiz memberOrderInfoBiz;

    @Resource
    private MemberOrderDetailInfoBiz memberOrderDetailInfoBiz;

    @Resource
    private MemberOrderPayInfoBiz memberOrderPayInfoBiz;

    @Resource
    private MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;

    @Resource
    private HotelDecorator hotelDecorator;

    @Resource
    private MemberInfoBiz memberInfoBiz;

    @Resource
    private CardConfigMedService cardConfigMedService;

    @Resource
    private RedisFactory redisFactory;

    @Resource
    private TurboMqProducerTemplate turboMqProducerTemplate;

    @Override
    public String addRecord(AddMemberCheckInRecordDto dto) {
        String memberNo = dto.getMemberNo();
        String guestOrderNo = dto.getGuestOrderNo();

        String idempotentKey = CommonUtil.concat(CommonConstant.CHECK_IN_RECORD_IDEMPOTENT_PREFIX, memberNo, guestOrderNo);
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this member check in record save is processing...memberNo:{};guestOrderNo:{}", memberNo, guestOrderNo);
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }

        try {
            MemberCheckInRecord existRecord = checkInRecordBiz.getByGuestOrderNo(dto.getBlocCode(), dto.getHotelCode(), memberNo, guestOrderNo);
            if (existRecord != null) {
                // 已存在，则不处理
                return existRecord.getRecordNo();
            }
            MemberCheckInRecord record = memberDataRecordMedConverter.convertDtoToPo(dto);
            record.setRecordNo(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextIdStr());
            checkInRecordBiz.add(record);

            // 发送MQ去汇总统计
            MemberCheckInSummaryMsgExt msg = new MemberCheckInSummaryMsgExt();
            msg.setMemberNo(memberNo);
            Message message = MessageHelper.builder().tag(TurboMqTopicTag.CHECK_IN_SUMMARY).msg(msg).topic(TurboMqTopic.PMS_MEMBER_BPS_TOPIC).build();
            SendResult sendResult = turboMqProducerTemplate.send(message);
            log.info("发送统计汇总MQ成功，消息ID:{}", sendResult.getMsgId());
            return record.getRecordNo();
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void summaryCheckInRecord(String memberNo) {
        String idempotentKey = CommonUtil.concat(CommonConstant.CHECK_IN_SUMMARY_IDEMPOTENT_PREFIX, memberNo);
        Boolean result = redisFactory.setNx(idempotentKey, 6, "1");
        //处理中...
        if (!result) {
            log.warn("this member check in record summary is processing...memberNo:{}", memberNo);
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }

        try {
            MemberCheckInStatistics blocStatistics = Optional.ofNullable(checkInStatisticsBiz.getBlocStatistics(memberNo)).orElse(new MemberCheckInStatistics());
            blocStatistics.setMemberNo(memberNo);
            blocStatistics.setHotelCode(String.valueOf(Constant.ZERO));
            // 酒店数据统计
            List<String> hotelCodes = checkInRecordBiz.getCheckInHotelByMemberNo(memberNo);
            List<MemberCheckInStatistics> hotelStatisticsList = Lists.newArrayList();
            for (String hotelCode : hotelCodes) {
                MemberCheckInStatistics hotelStatistics = Optional.ofNullable(checkInStatisticsBiz.getHotelStatistics(memberNo, hotelCode)).orElse(new MemberCheckInStatistics());
                hotelStatistics.setMemberNo(memberNo);
                hotelStatistics.setHotelCode(hotelCode);
                MemberCheckInRecord lastRecord = checkInRecordBiz.getLastByMemberNo(memberNo, hotelCode);
                if (lastRecord != null) {
                    hotelStatistics.setLastPrice(lastRecord.getLastPrice());
                    hotelStatistics.setLastCheckOutDate(lastRecord.getCheckOutDate());
                }
                PageMemberCheckInParamBo paramBo = new PageMemberCheckInParamBo();
                paramBo.setMemberNo(memberNo).setHotelCode(hotelCode);
                List<MemberCheckInRecord> checkInRecords = PageUtil.queryAll((int pageIndex, int pageSize) ->
                        checkInRecordBiz.pageCheckInRecord(paramBo.setPageIndex(pageIndex).setPageSize(pageSize)), null);
                if (!CollectionUtils.isEmpty(checkInRecords)) {
                    hotelStatistics.setCheckInCount(checkInRecords.size());
                    hotelStatistics.setCheckInNights(checkInRecords.stream().map(MemberCheckInRecord::getRoomNights).reduce(0, Integer::sum));
                    hotelStatistics.setExpenseAmount(checkInRecords.stream().map(e -> e.getRoomRate().add(e.getOtherRate())).reduce(BigDecimal.ZERO, BigDecimal::add));
                    hotelStatistics.setAveragePrice(checkInRecords.stream().map(MemberCheckInRecord::getAveragePrice).reduce(BigDecimal.ZERO, BigDecimal::add)
                            .divide(new BigDecimal(hotelStatistics.getCheckInNights()), Constant.TWO, RoundingMode.HALF_DOWN));
                } else {
                    hotelStatistics.setCheckInCount(0);
                    hotelStatistics.setCheckInNights(0);
                    hotelStatistics.setExpenseAmount(BigDecimal.ZERO);
                    hotelStatistics.setAveragePrice(BigDecimal.ZERO);
                }
                checkInStatisticsBiz.save(hotelStatistics);
                hotelStatisticsList.add(hotelStatistics);
            }
            // 集团数据统计
            MemberCheckInRecord lastRecord = checkInRecordBiz.getLastByMemberNo(memberNo);
            if (lastRecord != null) {
                blocStatistics.setLastPrice(lastRecord.getLastPrice());
                blocStatistics.setLastCheckOutDate(lastRecord.getCheckOutDate());
            }

            if (!CollectionUtils.isEmpty(hotelStatisticsList)) {
                blocStatistics.setCheckInCount(hotelStatisticsList.stream().map(MemberCheckInStatistics::getCheckInCount).reduce(0, Integer::sum));
                blocStatistics.setCheckInNights(hotelStatisticsList.stream().map(MemberCheckInStatistics::getCheckInNights).reduce(0, Integer::sum));
                blocStatistics.setExpenseAmount(hotelStatisticsList.stream().map(MemberCheckInStatistics::getExpenseAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
                blocStatistics.setAveragePrice(hotelStatisticsList.stream().map(MemberCheckInStatistics::getAveragePrice).reduce(BigDecimal.ZERO, BigDecimal::add)
                        .divide(new BigDecimal(hotelStatisticsList.size()), Constant.TWO, RoundingMode.HALF_DOWN));
            } else {
                blocStatistics.setCheckInCount(0);
                blocStatistics.setCheckInNights(0);
                blocStatistics.setExpenseAmount(BigDecimal.ZERO);
                blocStatistics.setAveragePrice(BigDecimal.ZERO);
            }
            checkInStatisticsBiz.save(blocStatistics);
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public Pageable<CheckInRecordDto> pageCheckinRecord(PageCheckinRecordDto dto) {
        PageMemberCheckInParamBo param = memberDataRecordMedConverter.convertDtoToBo(dto);
        Page<MemberCheckInRecord> page = checkInRecordBiz.pageCheckInRecord(param);
        List<MemberCheckInRecord> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return Pageable.empty();
        }
        List<String> hotelCodes = page.getRecords().stream().map(MemberCheckInRecord::getHotelCode).collect(Collectors.toList());
        List<HotelBaseInfoResp> hotelBaseInfos = hotelDecorator.listHotelBaseInfos(dto.getBlocCode(), hotelCodes);
        Map<String, String> hotelNameMap = hotelBaseInfos.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, HotelBaseInfoResp::getHotelName));
        return PageableUtil.convert(page, memberDataRecordMedConverter.convertPoToDto(records, hotelNameMap));
    }

    @Override
    public CheckInStatisticsDto getCheckInStatistics(String memberNo) {
        MemberCheckInStatistics statistics = checkInStatisticsBiz.getBlocStatistics(memberNo);
        if (statistics == null) {
            return new CheckInStatisticsDto();
        }
        return memberDataRecordMedConverter.convertPoToDto(statistics);
    }

    @Override
    public CheckInStatisticsDto getHotelCheckInStatistics(String memberNo, String hotelCode) {
        MemberCheckInStatistics statistics = checkInStatisticsBiz.getHotelStatistics(memberNo, hotelCode);
        if (statistics == null) {
            return new CheckInStatisticsDto();
        }
        return memberDataRecordMedConverter.convertPoToDto(statistics);
    }

    @Override
    public List<CheckInStatisticsDto> listByMemberNos(List<String> memberNos) {
        List<MemberCheckInStatistics> memberCheckInStatistics = checkInStatisticsBiz.listBlocStatistics(memberNos);
        return memberCheckInStatistics.stream().map(memberDataRecordMedConverter::convertPoToDto).collect(Collectors.toList());
    }

    @Override
    public Pageable<PurchaseCardRecordDto> pagePurchaseCardRecord(PagePurchaseCardRecordDto dto) {
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(dto.getMemberNo());
        Pageable<MemberOrderInfo> pageable = memberOrderInfoBiz.pageMemberPurchaseCardRecord(dto.getMasterType(), dto.getMasterCode(), dto.getMemberNo(), dto.getPageIndex(), dto.getPageSize(), dto.getBeginTime(), dto.getEndTime(), dto.getPlatformChannel());
        List<MemberOrderInfo> dataLst = pageable.getDatas();
        if (CollectionUtils.isEmpty(dataLst)) {
            return Pageable.empty();
        }
        List<String> memberOrderNoList = dataLst.stream().map(MemberOrderInfo::getMemberOrderNo).collect(Collectors.toList());
        List<MemberOrderDetailInfo> detailInfoList = memberOrderDetailInfoBiz.listByOrderNoList(memberOrderNoList);
        List<MemberOrderPayInfo> memberOrderPayInfoList = memberOrderPayInfoBiz.listByOrderNoList(memberOrderNoList);
        List<String> hotelCodes = dataLst.stream().filter(item -> item.getMasterType().equals(MasterTypeEnum.HOTEL.getCode())).map(MemberOrderInfo::getMasterCode).collect(Collectors.toList());
        List<HotelBaseInfoResp> hotelBaseInfos = CollectionUtils.isNotEmpty(hotelCodes) ? hotelDecorator.listHotelBaseInfos(null, hotelCodes) : Lists.newArrayList();
        Map<String, String> hotelBaseInfoMap = hotelBaseInfos.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, HotelBaseInfoResp::getHotelName, (k1, k2) -> k1));
        return PageableUtil.convert(pageable, memberDataRecordMedConverter.convertPoToPurchaseCardRecordDto(dataLst, detailInfoList, memberOrderPayInfoList, memberInfo, hotelBaseInfoMap));
    }

    @Override
    public Pageable<MemberLevelChangeRecordDto> pageMemberLevelChangeRecord(PageLevelChangeRecordDto dto) {
        Page<MemberCardLevelChangeRecord> page = memberCardLevelChangeRecordBiz.pageCardLevelChangeRecord(dto.getPageIndex(), dto.getPageSize(), dto.getMemberNo(), dto.getBeginTime(), dto.getEndTime());
        List<MemberCardLevelChangeRecord> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return Pageable.empty();
        }
        List<Long> cardIds = records.stream().map(MemberCardLevelChangeRecord::getCardId).distinct().collect(Collectors.toList());
        List<CardConfigDto> memberCardConfigRespList = cardConfigMedService.listCardConfig(dto.getMasterType(), dto.getMasterCode(), cardIds);
        ListCardLevelUpgradeRuleDto upgradeRuleDto = new ListCardLevelUpgradeRuleDto();
        upgradeRuleDto.setMasterType(dto.getMasterType()).setMasterCode(dto.getMasterCode()).setCardIds(cardIds);
        List<CardLevelUpgradeRuleDto> memberCardLevelUpgradeRules = cardConfigMedService.listCardLevelUpgradeRule(upgradeRuleDto);
        ListCardLevelRelegationRuleDto relegationRuleDto = new ListCardLevelRelegationRuleDto().setMasterType(dto.getMasterType())
                .setMasterCode(dto.getMasterCode()).setCardIds(cardIds);
        List<CardLevelRelegationRuleDto> memberCardLevelRelegationRules = cardConfigMedService.listCardLevelRelegationRule(relegationRuleDto);
        return PageableUtil.convert(page, memberDataRecordMedConverter.convertPoToMemberLevelChangeRecordDto(records, memberCardConfigRespList, memberCardLevelUpgradeRules, memberCardLevelRelegationRules));
    }

    @Override
    public MemberPayRegisterStatisticsResp getMemberPayStatistics(GetMemberPayRegisterStatisticsReq req) {
        MemberPayRegisterStatisticsResp memberPayRegisterStatisticsResp = new MemberPayRegisterStatisticsResp();
        List<MemberInfo> memberInfos = memberInfoBiz.listByHotelCode(req.getMasterType(), req.getMasterCode(), req.getHotelCode());
        List<String> memberNos = memberInfos.stream().map(MemberInfo::getMemberNo).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(memberNos)) {
            List<MemberOrderInfo> memberOrderInfos = memberOrderInfoBiz.countMemberRechargeRecord(req.getMasterType(), req.getMasterCode(), req.getBlocCode(), req.getHotelCode(), req.getStartTime(), req.getEndTime(), memberNos);
            memberPayRegisterStatisticsResp.setPayAmount(memberOrderInfos.stream().map(MemberOrderInfo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        } else {
            memberPayRegisterStatisticsResp.setPayAmount(BigDecimal.ZERO);
        }
        memberPayRegisterStatisticsResp.setRegisterCount(memberInfoBiz.selectCount(req.getMasterType(), req.getMasterCode(), req.getHotelCode(), req.getStartTime(), req.getEndTime()).intValue());
        return memberPayRegisterStatisticsResp;
    }

}
