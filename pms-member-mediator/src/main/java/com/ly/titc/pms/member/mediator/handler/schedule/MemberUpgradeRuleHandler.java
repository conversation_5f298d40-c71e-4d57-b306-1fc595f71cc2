package com.ly.titc.pms.member.mediator.handler.schedule;

import com.ly.titc.common.enums.StatusEnum;
import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.pms.member.biz.MemberCardInfoBiz;
import com.ly.titc.pms.member.biz.MemberCardLevelChangeRecordBiz;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.enums.CycleTypeEnum;
import com.ly.titc.pms.member.com.enums.StateEnum;
import com.ly.titc.pms.member.com.enums.SuccessfulPerformTypeEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.mediator.converter.ScheduleMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelConfigWithPrivilegeDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelUpgradeRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.ListCardLevelConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.ListCardLevelUpgradeRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateCardLevelDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.BaseCheckDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ConditionCheckResult;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberUpgradeRuleDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberUpgradeRuleDto;
import com.ly.titc.pms.member.mediator.service.CardConfigMedService;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.watcher.common.utils.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberUpgradeRuleHandler
 * @Date：2024-11-21 14:24
 * @Filename：MemberUpgradeRuleHandler
 */
@Component
public class MemberUpgradeRuleHandler extends AbstractScheduleHandler<MemberUpgradeRuleDto> {

    @Resource
    private ScheduleMedConverter scheduleMedConverter;

    @Resource
    private CardConfigMedService cardConfigMedService;

    @Resource
    private MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;

    @Resource
    private MemberCardInfoBiz memberCardInfoBiz;

    @Resource
    private MemberCardMedService memberCardMedService;

    @Override
    public void process(Integer masterType, String masterCode, String memberNo) {
        // 查询这个会员的所有会员卡
        List<MemberCardInfo> memberCardInfos = memberCardInfoBiz.listByMemberNo(masterType, masterCode, memberNo);
        if (CollectionUtils.isEmpty(memberCardInfos)) {
            return;
        }
        // 查询每个卡对应的升降级规则
        List<Long> cardIds = memberCardInfos.stream().map(MemberCardInfo::getCardId).collect(Collectors.toList());
        ListCardLevelUpgradeRuleDto upgradeRuleDto = new ListCardLevelUpgradeRuleDto();
        upgradeRuleDto
                .setMasterType(masterType)
                .setMasterCode(masterCode)
                .setCardIds(cardIds)
                .setState(StatusEnum.VALID.getStatus());
        List<CardLevelUpgradeRuleDto> rules = cardConfigMedService.listCardLevelUpgradeRule(upgradeRuleDto);
        Map<String, CardLevelUpgradeRuleDto> ruleMap = rules.stream().collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getSourceLevel()), Function.identity()));
        // 查询卡等级信息
        ListCardLevelConfigDto levelConfigDto = new ListCardLevelConfigDto();
        levelConfigDto.setMasterType(masterType).setMasterCode(masterCode).setCardIds(cardIds).setState(StateEnum.VALID.getState());
        List<CardLevelConfigWithPrivilegeDto> memberCardLevelInfos = cardConfigMedService.listCardLevelConfig(levelConfigDto);
        Map<String, CardLevelConfigWithPrivilegeDto> memberCardLevelInfoMap = memberCardLevelInfos.stream().collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getCardLevel()), Function.identity()));

        for (MemberCardInfo memberCardInfo : memberCardInfos) {
            Long cardId = memberCardInfo.getCardId();
            Integer cardLevel = memberCardInfo.getCardLevel();
            CardLevelUpgradeRuleDto rule = ruleMap.get(String.format("%s-%s", cardId, cardLevel));
            // 如果没有找到对应的升级规则，跳过这张卡
            if (rule == null) {
                continue;
            }
            MemberUpgradeRuleDto ruleDto = scheduleMedConverter.convertUpgradeRuleDto(rule);
            if (ruleDto == null) {
                continue;
            }
            MemberCardLevelChangeRecord memberCardLevelChangeRecord;
            LocalDateTime startDate;
            LocalDateTime endDate = LocalDateTime.now();
            if (ruleDto.getCycleType().equals(CycleTypeEnum.SINCE_REGISTER.getType())) {
                // 获取这个会员的注册日期
                memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo,
                        Arrays.asList(ChangeTypeEnum.REGISTER.getType(), ChangeTypeEnum.ISSUE.getType()));
                startDate = memberCardLevelChangeRecord != null ? memberCardLevelChangeRecord.getGmtCreate() : LocalDateTime.now().minusYears(1);
            } else {
                // 获取这个会员的上次升降级日期
                List<Integer> changeTypeList = Arrays.asList(ChangeTypeEnum.DOWN_AUTO.getType(), ChangeTypeEnum.UPGRADE_AUTO.getType(),
                        ChangeTypeEnum.UPGRADE_ARTIFICIAL.getType(), ChangeTypeEnum.UPGRADE_PURCHASE.getType());
                memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo, changeTypeList);
                startDate = memberCardLevelChangeRecord != null ? memberCardLevelChangeRecord.getGmtCreate() : LocalDateTime.now().minusYears(1);
            }
            // 获取会员的各种记录数据
            List<BaseCheckDto> pointRecords = getMemberPointRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> consumptionRecords = getMemberConsumptionRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> rechargeRecords = getMemberRechargeRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> checkoutRecords = getMemberCheckoutRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> stayRecords = getMemberStayRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> unstayRecords = getMemberUnstayRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> avgRoomFeeRecords = getMemberAvgRoomFeeRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> registerDaysRecords = getMemberRegisterDaysRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            // 检查升级条件
            ConditionCheckResult checkResult;
            if (ruleDto.getUpgradeSuccessfulPerformType().equals(SuccessfulPerformTypeEnum.ALL.getType())) {
                checkResult = checkAllConditionsWithDetail(ruleDto.getDetails(), memberNo, pointRecords, consumptionRecords, rechargeRecords, checkoutRecords, stayRecords, unstayRecords, avgRoomFeeRecords, registerDaysRecords);
            } else {
                checkResult = checkAnyConditionWithDetail(ruleDto.getDetails(), memberNo, pointRecords, consumptionRecords, rechargeRecords, checkoutRecords, stayRecords, unstayRecords, avgRoomFeeRecords, registerDaysRecords);
            }
            if (checkResult.isPassed()) {
                CardLevelConfigWithPrivilegeDto beforeLevelInfo = memberCardLevelInfoMap.get(String.format("%s-%s", cardId, cardLevel));
                CardLevelConfigWithPrivilegeDto afterLevelInfo = memberCardLevelInfoMap.get(String.format("%s-%s", cardId, ruleDto.getTargetLevel()));
                if (beforeLevelInfo == null || afterLevelInfo == null) {
                    continue;
                }
                // 升级会员卡等级
                UpdateCardLevelDto updateCardLevelDto = new UpdateCardLevelDto();
                updateCardLevelDto.setMemberNo(memberNo)
                        .setCardId(cardId)
                        .setMemberCardNo(memberCardInfo.getMemberCardNo())
                        .setPreLevel(cardLevel)
                        .setPreLevelName(beforeLevelInfo.getCardLevelName())
                        .setAfterLevel(ruleDto.getTargetLevel())
                        .setAfterLevelName(afterLevelInfo.getCardLevelName())
                        .setEffectBeginDate(LocalDateUtil.formatByNormalDate(LocalDate.now()))
                        .setEffectEndDate(afterLevelInfo.getIsLongTerm() == 1 ? null : LocalDateUtil.formatByNormalDate(LocalDate.now().plusDays(afterLevelInfo.getValidPeriod())))
                        .setIsLongTerm(afterLevelInfo.getIsLongTerm())
                        .setChangeType(ChangeTypeEnum.UPGRADE_AUTO.getType())
                        .setReason("自动升级：" + checkResult.generateReason())
                        .setOperator("SYSTEM");
                memberCardMedService.updateCardLevel(updateCardLevelDto);
            }
        }
    }
    
    @Override
    public Integer getAction() {
        return ScheduleHandlerEnum.MEMBER_UPGRADE_RULE.getAction();
    }
}
