package com.ly.titc.pms.member.mediator.handler.schedule;

import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.pms.member.biz.MemberCardInfoBiz;
import com.ly.titc.pms.member.biz.MemberCardLevelChangeRecordBiz;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.enums.CycleTypeEnum;
import com.ly.titc.pms.member.com.enums.StateEnum;
import com.ly.titc.pms.member.com.enums.SuccessfulPerformTypeEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.mediator.converter.ScheduleMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelConfigWithPrivilegeDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelRelegationRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.ListCardLevelConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.ListCardLevelRelegationRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateCardLevelDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.BaseCheckDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ConditionCheckResult;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberRelegationRuleDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberRelegationRuleDto;
import com.ly.titc.pms.member.mediator.service.CardConfigMedService;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.watcher.common.utils.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberRelegationRuleHandler
 * @Date：2024-11-21 14:25
 * @Filename：MemberRelegationRuleHandler
 */
@Component
public class MemberRelegationRuleHandler extends AbstractScheduleHandler<MemberRelegationRuleDto> {

    @Resource
    private ScheduleMedConverter scheduleMedConverter;

    @Resource
    private CardConfigMedService cardConfigMedService;

    @Resource
    private MemberCardInfoBiz memberCardInfoBiz;

    @Resource
    private MemberCardMedService memberCardMedService;

    @Resource
    private MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;

    @Override
    public void process(Integer masterType, String masterCode, String memberNo) {
        // 查询这个会员的所有会员卡
        List<MemberCardInfo> memberCardInfos = memberCardInfoBiz.listByMemberNo(masterType, masterCode, memberNo);
        if (CollectionUtils.isEmpty(memberCardInfos)) {
            return;
        }
        // 查询每个卡对应的规则
        List<Long> cardIds = memberCardInfos.stream().map(MemberCardInfo::getCardId).collect(Collectors.toList());
        ListCardLevelRelegationRuleDto relegationRuleDto = new ListCardLevelRelegationRuleDto()
                .setMasterType(masterType)
                .setMasterCode(masterCode)
                .setCardIds(cardIds)
                .setState(StateEnum.VALID.getState());
        List<CardLevelRelegationRuleDto> rules = cardConfigMedService.listCardLevelRelegationRule(relegationRuleDto);
        Map<String, CardLevelRelegationRuleDto> ruleMap = rules.stream()
                .collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getSourceLevel()), Function.identity()));

        // 查询卡等级信息
        ListCardLevelConfigDto levelConfigDto = new ListCardLevelConfigDto()
                .setMasterType(masterType)
                .setMasterCode(masterCode)
                .setCardIds(cardIds)
                .setState(StateEnum.VALID.getState());
        List<CardLevelConfigWithPrivilegeDto> memberCardLevelInfos = cardConfigMedService.listCardLevelConfig(levelConfigDto);
        Map<String, CardLevelConfigWithPrivilegeDto> memberCardLevelInfoMap = memberCardLevelInfos.stream()
                .collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getCardLevel()), Function.identity()));
        for (MemberCardInfo memberCardInfo : memberCardInfos) {
            Long cardId = memberCardInfo.getCardId();
            Integer cardLevel = memberCardInfo.getCardLevel();
            CardLevelRelegationRuleDto rule = ruleMap.get(String.format("%s-%s", cardId, cardLevel));
            // 如果没有找到对应的保级规则，跳过这张卡
            if (rule == null) {
                continue;
            }
            MemberRelegationRuleDto ruleDto = scheduleMedConverter.convertRelegationRuleDto(rule);
            if (ruleDto == null) {
                continue;
            }
            MemberCardLevelChangeRecord memberCardLevelChangeRecord;
            LocalDateTime startDate;
            LocalDateTime endDate = LocalDateTime.now();
            if (ruleDto.getCycleType().equals(CycleTypeEnum.SINCE_REGISTER.getType())) {
                // 获取这个会员的注册日期
                memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo,
                        Arrays.asList(ChangeTypeEnum.REGISTER.getType(), ChangeTypeEnum.ISSUE.getType()));
                startDate = memberCardLevelChangeRecord != null ? memberCardLevelChangeRecord.getGmtCreate() : LocalDateTime.now().minusYears(1);
            } else {
                // 获取这个会员的上次升降级日期
                memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo,
                        Arrays.asList(ChangeTypeEnum.DOWN_AUTO.getType(), ChangeTypeEnum.UPGRADE_AUTO.getType(),
                                ChangeTypeEnum.UPGRADE_ARTIFICIAL.getType(), ChangeTypeEnum.UPGRADE_PURCHASE.getType(), ChangeTypeEnum.SUCCESS.getType()));
                startDate = memberCardLevelChangeRecord != null ? memberCardLevelChangeRecord.getGmtCreate() : LocalDateTime.now().minusYears(1);
            }
            // 获取会员的各种记录数据
            List<BaseCheckDto> pointRecords = getMemberPointRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> consumptionRecords = getMemberConsumptionRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> rechargeRecords = getMemberRechargeRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> checkoutRecords = getMemberCheckoutRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> stayRecords = getMemberStayRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> unstayRecords = getMemberUnstayRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> avgRoomFeeRecords = getMemberAvgRoomFeeRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> registerDaysRecords = getMemberRegisterDaysRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            // 检查保级条件
            ConditionCheckResult checkResult;
            if (ruleDto.getRelegationSuccessfulPerformType().equals(SuccessfulPerformTypeEnum.ALL.getType())) {
                checkResult = checkAllConditionsWithDetail(ruleDto.getDetails(), memberNo, pointRecords, consumptionRecords, rechargeRecords,
                        checkoutRecords, stayRecords, unstayRecords, avgRoomFeeRecords, registerDaysRecords);
            } else {
                checkResult = checkAnyConditionWithDetail(ruleDto.getDetails(), memberNo, pointRecords, consumptionRecords, rechargeRecords,
                        checkoutRecords, stayRecords, unstayRecords, avgRoomFeeRecords, registerDaysRecords);
            }
            // 对于降级规则，需要反转结果：如果条件满足则保级成功，如果条件不满足则降级
            boolean shouldDowngrade = !checkResult.isPassed();
            CardLevelConfigWithPrivilegeDto currentLevelInfo = memberCardLevelInfoMap.get(String.format("%s-%s", cardId, cardLevel));
            if (currentLevelInfo == null) {
                continue;
            }
            if (shouldDowngrade) {
                // 保级失败，执行降级
                CardLevelConfigWithPrivilegeDto afterLevelInfo = memberCardLevelInfoMap.get(String.format("%s-%s", cardId, ruleDto.getTargetLevel()));
                if (afterLevelInfo == null) {
                    continue;
                }
                // 降级会员卡等级
                UpdateCardLevelDto updateCardLevelDto = new UpdateCardLevelDto();
                updateCardLevelDto.setMemberNo(memberNo)
                        .setCardId(cardId)
                        .setMemberCardNo(memberCardInfo.getMemberCardNo())
                        .setPreLevel(cardLevel)
                        .setPreLevelName(currentLevelInfo.getCardLevelName())
                        .setAfterLevel(ruleDto.getTargetLevel())
                        .setAfterLevelName(afterLevelInfo.getCardLevelName())
                        .setEffectBeginDate(LocalDateUtil.formatByNormalDate(LocalDate.now()))
                        .setEffectEndDate(afterLevelInfo.getIsLongTerm() == 1 ? null : LocalDateUtil.formatByNormalDate(LocalDate.now().plusDays(afterLevelInfo.getValidPeriod())))
                        .setIsLongTerm(afterLevelInfo.getIsLongTerm())
                        .setChangeType(ChangeTypeEnum.DOWN_AUTO.getType())
                        .setReason("自动降级：" + checkResult.generateReason())
                        .setOperator("SYSTEM")
                        .setBizType("SCHEDULE")
                        .setBizNo(String.valueOf(ruleDto.getId()));
                memberCardMedService.updateCardLevel(updateCardLevelDto);
            } else {
                // 保级成功，记录保级成功信息
                String successReason = "保级成功：" + checkResult.generateReason();
                UpdateCardLevelDto updateCardLevelDto = new UpdateCardLevelDto();
                updateCardLevelDto.setMemberNo(memberNo)
                        .setCardId(cardId)
                        .setMemberCardNo(memberCardInfo.getMemberCardNo())
                        .setPreLevel(cardLevel)
                        .setPreLevelName(currentLevelInfo.getCardLevelName())
                        .setAfterLevel(cardLevel) // 保级成功，等级不变
                        .setAfterLevelName(currentLevelInfo.getCardLevelName())
                        .setEffectBeginDate(LocalDateUtil.formatByNormalDate(LocalDate.now()))
                        .setEffectEndDate(currentLevelInfo.getIsLongTerm() == 1 ? null : LocalDateUtil.formatByNormalDate(LocalDate.now().plusDays(currentLevelInfo.getValidPeriod())))
                        .setIsLongTerm(currentLevelInfo.getIsLongTerm())
                        .setChangeType(ChangeTypeEnum.SUCCESS.getType())
                        .setReason(successReason)
                        .setOperator("SYSTEM")
                        .setBizType("SCHEDULE")
                        .setBizNo(String.valueOf(ruleDto.getId()));
                memberCardMedService.updateCardLevel(updateCardLevelDto);
            }
        }
    }


    @Override
    public Integer getAction() {
        return ScheduleHandlerEnum.MEMBER_RELEGATION_RULE.getAction();
    }
}
