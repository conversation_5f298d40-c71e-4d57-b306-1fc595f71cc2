package com.ly.titc.pms.member.mediator.handler.schedule;

import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.pms.member.biz.MemberCardInfoBiz;
import com.ly.titc.pms.member.biz.MemberCardLevelChangeRecordBiz;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.enums.CycleTypeEnum;
import com.ly.titc.pms.member.com.enums.StateEnum;
import com.ly.titc.pms.member.com.enums.SuccessfulPerformTypeEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.mediator.converter.ScheduleMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelConfigWithPrivilegeDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.CardLevelRelegationRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.ListCardLevelConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.card.ListCardLevelRelegationRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateCardLevelDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.BaseCheckDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.ConditionCheckResult;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberRelegationRuleDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberRelegationRuleDto;
import com.ly.titc.pms.member.mediator.service.CardConfigMedService;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.watcher.common.utils.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberRelegationRuleHandler
 * @Date：2024-11-21 14:25
 * @Filename：MemberRelegationRuleHandler
 */
@Component
public class MemberRelegationRuleHandler extends AbstractScheduleHandler<MemberRelegationRuleDto> {

    @Resource
    private ScheduleMedConverter scheduleMedConverter;

    @Resource
    private CardConfigMedService cardConfigMedService;

    @Resource
    private MemberCardInfoBiz memberCardInfoBiz;

    @Resource
    private MemberCardMedService memberCardMedService;

    @Resource
    private MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;

    @Override
    public void process(Integer masterType, String masterCode, String memberNo) {
        // 查询这个会员的所有会员卡
        List<MemberCardInfo> memberCardInfos = memberCardInfoBiz.listByMemberNo(masterType, masterCode, memberNo);
        if (CollectionUtils.isEmpty(memberCardInfos)) {
            return;
        }

        // 查询每个卡对应的规则
        List<Long> cardIds = memberCardInfos.stream().map(MemberCardInfo::getCardId).collect(Collectors.toList());
        ListCardLevelRelegationRuleDto relegationRuleDto = new ListCardLevelRelegationRuleDto()
                .setMasterType(masterType)
                .setMasterCode(masterCode)
                .setCardIds(cardIds)
                .setState(StateEnum.VALID.getState());
        List<CardLevelRelegationRuleDto> rules = cardConfigMedService.listCardLevelRelegationRule(relegationRuleDto);
        Map<String, CardLevelRelegationRuleDto> ruleMap = rules.stream()
                .collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getSourceLevel()), Function.identity()));

        // 查询卡等级信息
        ListCardLevelConfigDto levelConfigDto = new ListCardLevelConfigDto()
                .setMasterType(masterType)
                .setMasterCode(masterCode)
                .setCardIds(cardIds)
                .setState(StateEnum.VALID.getState());
        List<CardLevelConfigWithPrivilegeDto> memberCardLevelInfos = cardConfigMedService.listCardLevelConfig(levelConfigDto);
        Map<String, CardLevelConfigWithPrivilegeDto> memberCardLevelInfoMap = memberCardLevelInfos.stream()
                .collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getCardLevel()), Function.identity()));

        for (MemberCardInfo memberCardInfo : memberCardInfos) {
            Long cardId = memberCardInfo.getCardId();
            Integer cardLevel = memberCardInfo.getCardLevel();
            CardLevelRelegationRuleDto rule = ruleMap.get(String.format("%s-%s", cardId, cardLevel));
            // 如果没有找到对应的保级规则，跳过这张卡
            if (rule == null) {
                continue;
            }
            MemberRelegationRuleDto ruleDto = scheduleMedConverter.convertRelegationRuleDto(rule);
            if (ruleDto == null) {
                continue;
            }

            // 根据统计周期确定开始时间
            MemberCardLevelChangeRecord memberCardLevelChangeRecord;
            LocalDateTime startDate;
            LocalDateTime endDate = LocalDateTime.now();
            if (ruleDto.getCycleType().equals(CycleTypeEnum.SINCE_REGISTER.getType())) {
                // 获取这个会员的注册日期
                memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo,
                        Arrays.asList(ChangeTypeEnum.REGISTER.getType(), ChangeTypeEnum.ISSUE.getType()));
                startDate = memberCardLevelChangeRecord != null ? memberCardLevelChangeRecord.getGmtCreate() : LocalDateTime.now().minusYears(1);
            } else {
                // 获取这个会员的上次升降级日期
                memberCardLevelChangeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo,
                        Arrays.asList(ChangeTypeEnum.DOWN_AUTO.getType(), ChangeTypeEnum.UPGRADE_AUTO.getType(),
                                ChangeTypeEnum.UPGRADE_ARTIFICIAL.getType(), ChangeTypeEnum.UPGRADE_PURCHASE.getType(), ChangeTypeEnum.SUCCESS.getType()));
                startDate = memberCardLevelChangeRecord != null ? memberCardLevelChangeRecord.getGmtCreate() : LocalDateTime.now().minusYears(1);
            }

            // 获取会员的各种记录数据（根据具体的时间范围查询）
            List<BaseCheckDto> pointRecords = getMemberPointRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> consumptionRecords = getMemberConsumptionRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> rechargeRecords = getMemberRechargeRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> checkoutRecords = getMemberCheckoutRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> stayRecords = getMemberStayRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> unstayRecords = getMemberUnstayRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> avgRoomFeeRecords = getMemberAvgRoomFeeRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);
            List<BaseCheckDto> registerDaysRecords = getMemberRegisterDaysRecordByMemberNo(masterType, masterCode, Arrays.asList(memberNo), startDate, endDate);

            // 检查保级条件
            ConditionCheckResult checkResult;
            if (ruleDto.getRelegationSuccessfulPerformType().equals(SuccessfulPerformTypeEnum.ALL.getType())) {
                checkResult = checkAllConditionsWithDetail(ruleDto.getDetails(), memberNo, pointRecords, consumptionRecords, rechargeRecords,
                        checkoutRecords, stayRecords, unstayRecords, avgRoomFeeRecords, registerDaysRecords);
            } else {
                checkResult = checkAnyConditionWithDetail(ruleDto.getDetails(), memberNo, pointRecords, consumptionRecords, rechargeRecords,
                        checkoutRecords, stayRecords, unstayRecords, avgRoomFeeRecords, registerDaysRecords);
            }

            // 对于降级规则，需要反转结果：如果条件满足则保级成功，如果条件不满足则降级
            boolean shouldDowngrade = !checkResult.isPassed();
            CardLevelConfigWithPrivilegeDto currentLevelInfo = memberCardLevelInfoMap.get(String.format("%s-%s", cardId, cardLevel));
            if (currentLevelInfo == null) {
                continue;
            }

            if (shouldDowngrade) {
                // 保级失败，执行降级 - 计算降级目标等级
                Integer targetLevel = calculateDowngradeTargetLevel(memberNo, memberCardInfo.getMemberCardNo(), cardLevel);

                // 如果已经是1级或者计算出的目标等级无效，则不降级
                if (targetLevel == null || targetLevel >= cardLevel) {
                    continue;
                }

                CardLevelConfigWithPrivilegeDto afterLevelInfo = memberCardLevelInfoMap.get(String.format("%s-%s", cardId, targetLevel));
                if (afterLevelInfo == null) {
                    continue;
                }

                // 降级会员卡等级
                UpdateCardLevelDto updateCardLevelDto = new UpdateCardLevelDto();
                updateCardLevelDto.setMemberNo(memberNo)
                        .setCardId(cardId)
                        .setMemberCardNo(memberCardInfo.getMemberCardNo())
                        .setPreLevel(cardLevel)
                        .setPreLevelName(currentLevelInfo.getCardLevelName())
                        .setAfterLevel(targetLevel)
                        .setAfterLevelName(afterLevelInfo.getCardLevelName())
                        .setEffectBeginDate(LocalDateUtil.formatByNormalDate(LocalDate.now()))
                        .setEffectEndDate(afterLevelInfo.getIsLongTerm() == 1 ? null : LocalDateUtil.formatByNormalDate(LocalDate.now().plusDays(afterLevelInfo.getValidPeriod())))
                        .setIsLongTerm(afterLevelInfo.getIsLongTerm())
                        .setChangeType(ChangeTypeEnum.DOWN_AUTO.getType())
                        .setReason("自动降级：" + checkResult.generateReason())
                        .setOperator("SYSTEM")
                        .setBizType("SCHEDULE")
                        .setBizNo(String.valueOf(ruleDto.getId()));
                memberCardMedService.updateCardLevel(updateCardLevelDto);
            } else {
                // 保级成功，记录保级成功信息
                String successReason = "保级成功：" + checkResult.generateReason();
                UpdateCardLevelDto updateCardLevelDto = new UpdateCardLevelDto();
                updateCardLevelDto.setMemberNo(memberNo)
                        .setCardId(cardId)
                        .setMemberCardNo(memberCardInfo.getMemberCardNo())
                        .setPreLevel(cardLevel)
                        .setPreLevelName(currentLevelInfo.getCardLevelName())
                        .setAfterLevel(cardLevel) // 保级成功，等级不变
                        .setAfterLevelName(currentLevelInfo.getCardLevelName())
                        .setEffectBeginDate(LocalDateUtil.formatByNormalDate(LocalDate.now()))
                        .setEffectEndDate(currentLevelInfo.getIsLongTerm() == 1 ? null : LocalDateUtil.formatByNormalDate(LocalDate.now().plusDays(currentLevelInfo.getValidPeriod())))
                        .setIsLongTerm(currentLevelInfo.getIsLongTerm())
                        .setChangeType(ChangeTypeEnum.SUCCESS.getType())
                        .setReason(successReason)
                        .setOperator("SYSTEM")
                        .setBizType("SCHEDULE")
                        .setBizNo(String.valueOf(ruleDto.getId()));
                memberCardMedService.updateCardLevel(updateCardLevelDto);
            }
        }
    }

    /**
     * 计算降级目标等级
     * 逻辑：降级到升级之前的等级，也就是查询member_card_level_change_record表获取之前的等级，
     * 如果查询不到就减1，但如果已经是1了，就不降级
     */
    private Integer calculateDowngradeTargetLevel(String memberNo, String memberCardNo, Integer currentLevel) {
        // 如果当前等级已经是1，不能再降级
        if (currentLevel <= 1) {
            return null;
        }

        // 查询该会员卡的最近一次升级记录
        MemberCardLevelChangeRecord lastUpgradeRecord = memberCardLevelChangeRecordBiz.getMemberLastedRecordByType(memberNo,
                Arrays.asList(ChangeTypeEnum.UPGRADE_AUTO.getType(), ChangeTypeEnum.UPGRADE_ARTIFICIAL.getType(),
                        ChangeTypeEnum.UPGRADE_PURCHASE.getType()));

        // 如果找到了升级记录，且升级后的等级是当前等级，则降级到升级前的等级
        if (lastUpgradeRecord != null && lastUpgradeRecord.getMemberCardNo().equals(memberCardNo)
                && lastUpgradeRecord.getAfterLevel().equals(currentLevel)) {
            return lastUpgradeRecord.getPreLevel();
        }

        // 如果没有找到升级记录，则降级到当前等级-1
        return currentLevel - 1;
    }

    /**
     * 根据时间范围过滤记录数据
     */
    private List<BaseCheckDto> filterRecordsByDateRange(List<BaseCheckDto> records, LocalDateTime startDate, LocalDateTime endDate) {
        if (CollectionUtils.isEmpty(records)) {
            return records;
        }
        // 这里需要根据BaseCheckDto的实际字段来实现时间过滤
        // 由于不知道BaseCheckDto的具体时间字段，这里先返回原数据
        // 实际使用时需要根据具体的时间字段进行过滤
        return records;
    }


    @Override
    public Integer getAction() {
        return ScheduleHandlerEnum.MEMBER_RELEGATION_RULE.getAction();
    }
}
