package com.ly.titc.pms.member.biz;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.dal.dao.CardApplicableDataMappingDao;
import com.ly.titc.pms.member.dal.dao.CardConfigInfoDao;
import com.ly.titc.pms.member.dal.entity.po.CardApplicableDataMapping;
import com.ly.titc.pms.member.dal.entity.po.CardConfigInfo;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * 卡配置
 *
 * <AUTHOR>
 * @date 2025/6/25 11:45
 */
@Component
public class CardConfigBiz {

    @Resource
    private CardConfigInfoDao cardConfigInfoDao;

    @Resource
    private CardApplicableDataMappingDao cardApplicableDataMappingDao;

    @Transactional(rollbackFor = Exception.class)
    public void insert(CardConfigInfo info, List<CardApplicableDataMapping> mappings) {
        cardConfigInfoDao.insert(info);
        if (CollectionUtil.isNotEmpty(mappings)) {
            cardApplicableDataMappingDao.insertBatch(mappings);
        }
    }

    public CardConfigInfo getByCardId(Long id) {
        return cardConfigInfoDao.selectById(id);
    }

    public CardConfigInfo getByCardId(Integer masterType, String masterCode, Long cardId) {
        LambdaUpdateWrapper<CardConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardConfigInfo::getMasterType, masterType)
                .eq(CardConfigInfo::getMasterCode, masterCode)
                .eq(CardConfigInfo::getId, cardId);
        return cardConfigInfoDao.selectOne(wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public void update(CardConfigInfo cardConfigInfo, List<CardApplicableDataMapping> mappings) {
        LambdaUpdateWrapper<CardConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardConfigInfo::getId, cardConfigInfo.getId());
        wrapper.set(CardConfigInfo::getCardType, cardConfigInfo.getCardType())
                .set(CardConfigInfo::getCardName, cardConfigInfo.getCardName())
                .set(CardConfigInfo::getApplicationType, cardConfigInfo.getApplicationType())
                .set(CardConfigInfo::getApplicationScope, cardConfigInfo.getApplicationScope())
                .set(CardConfigInfo::getMobileInput, cardConfigInfo.getMobileInput())
                .set(CardConfigInfo::getNameInput, cardConfigInfo.getNameInput())
                .set(CardConfigInfo::getCertificateInput, cardConfigInfo.getCertificateInput())
                .set(CardConfigInfo::getIsDefault, cardConfigInfo.getIsDefault())
                .set(CardConfigInfo::getDescription, cardConfigInfo.getDescription())
                .set(CardConfigInfo::getState, cardConfigInfo.getState())
                .set(CardConfigInfo::getSort, cardConfigInfo.getSort())
                .set(CardConfigInfo::getModifyUser, cardConfigInfo.getModifyUser());
        cardConfigInfoDao.updateById(cardConfigInfo);

        cardApplicableDataMappingDao.delete(new LambdaQueryWrapper<CardApplicableDataMapping>()
                .eq(CardApplicableDataMapping::getCardId, cardConfigInfo.getId()));
        if (CollectionUtil.isNotEmpty(mappings)) {
            cardApplicableDataMappingDao.insertBatch(mappings);
        }
    }

    public void updateAllIsDefault(Integer masterType, String masterCode) {
        LambdaUpdateWrapper<CardConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardConfigInfo::getMasterType, masterType)
                .eq(CardConfigInfo::getMasterCode, masterCode);
        wrapper.set(CardConfigInfo::getIsDefault, Constant.ZERO);
        cardConfigInfoDao.update(null, wrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteByCardId(Long cardId, String operator) {
        LambdaUpdateWrapper<CardConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardConfigInfo::getId, cardId);
        wrapper.set(CardConfigInfo::getIsDelete, Constant.ONE)
                .set(CardConfigInfo::getModifyUser, operator);
        cardConfigInfoDao.update(null, wrapper);

        LambdaUpdateWrapper<CardApplicableDataMapping> mappingWrapper = new LambdaUpdateWrapper<>();
        mappingWrapper.eq(CardApplicableDataMapping::getCardId, cardId);
        mappingWrapper.set(CardApplicableDataMapping::getIsDelete, Constant.ONE)
                .set(CardApplicableDataMapping::getModifyUser, operator);
        cardApplicableDataMappingDao.update(null, mappingWrapper);
    }

    public CardConfigInfo getDefaultCard(Integer masterType, String masterCode) {
        LambdaUpdateWrapper<CardConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardConfigInfo::getMasterType, masterType)
                .eq(CardConfigInfo::getMasterCode, masterCode)
                .eq(CardConfigInfo::getIsDefault, Constant.ONE);
        return cardConfigInfoDao.selectOne(wrapper);
    }

    public List<CardConfigInfo> listByIds(Integer masterType, String masterCode, List<Long> cardIds) {
        LambdaUpdateWrapper<CardConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardConfigInfo::getMasterType, masterType)
                .eq(CardConfigInfo::getMasterCode, masterCode)
                .in(CardConfigInfo::getId, cardIds);
        return cardConfigInfoDao.selectList(wrapper);
    }

    public List<CardApplicableDataMapping> lisApplicableDataMapping(Long cardId) {
        LambdaUpdateWrapper<CardApplicableDataMapping> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CardApplicableDataMapping::getCardId, cardId);
        return cardApplicableDataMappingDao.selectList(wrapper);
    }

    public List<CardApplicableDataMapping> lisApplicableDataMapping(List<Long> cardIds) {
        LambdaUpdateWrapper<CardApplicableDataMapping> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CardApplicableDataMapping::getCardId, cardIds);
        return cardApplicableDataMappingDao.selectList(wrapper);
    }

    public List<CardConfigInfo> listByMaster(Integer masterType, String masterCode, Long cardId) {
        LambdaUpdateWrapper<CardConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(CardConfigInfo::getMasterType, masterType)
                .eq(CardConfigInfo::getMasterCode, masterCode)
                .eq(cardId != null, CardConfigInfo::getId, cardId);
        return cardConfigInfoDao.selectList(wrapper);
    }

    public List<CardConfigInfo> listByCardIds(Integer masterType, String masterCode, List<Long> cardIds) {
        LambdaUpdateWrapper<CardConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CardConfigInfo::getId, cardIds)
                .eq(CardConfigInfo::getMasterType, masterType)
                .eq(CardConfigInfo::getMasterCode, masterCode);
        return cardConfigInfoDao.selectList(wrapper);
    }

    public CardConfigInfo getByCardName(Integer masterType, String masterCode, String cardName) {
        LambdaUpdateWrapper<CardConfigInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CardConfigInfo::getCardName, cardName)
                .eq(CardConfigInfo::getMasterType, masterType)
                .eq(CardConfigInfo::getMasterCode, masterCode);
        return cardConfigInfoDao.selectOne(wrapper);
    }
}
